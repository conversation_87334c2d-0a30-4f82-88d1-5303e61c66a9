using UnityEngine;

public class FallingState : PlayerState
{
    public FallingState(PlayerMovement player, PlayerStateMachine stateMachine) : base(player, stateMachine)
    {
    }

    public override void Enter()
    {
        Debug.Log("Entering Falling State");
    }

    public override void Update()
    {
        // Allow air control while falling
        if (player.MoveInput.magnitude > 0.1f)
        {
            Vector3 airMovement = new Vector3(player.MoveInput.x, 0, player.MoveInput.y) * player.moveSpeed * 0.3f; // Even less air control than jumping
            player.Rb.linearVelocity = new Vector3(airMovement.x, player.Rb.linearVelocity.y, airMovement.z);
        }

        // Check if we've landed
        if (player.IsGrounded)
        {
            if (player.MoveInput.magnitude > 0.1f)
            {
                stateMachine.ChangeState(player.MovingState);
            }
            else
            {
                stateMachine.ChangeState(player.IdleState);
            }
        }
    }
}
