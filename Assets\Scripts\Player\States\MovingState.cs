using UnityEngine;

public class MovingState : PlayerState
{
    public MovingState(PlayerMovement player, PlayerStateMachine stateMachine) : base(player, stateMachine)
    {
    }

    public override void Enter()
    {
        Debug.Log("Entering Moving State");
    }

    public override void Update()
    {
        // Check for state transitions
        if (player.MoveInput.magnitude <= 0.1f)
        {
            stateMachine.ChangeState(player.IdleState);
        }

        if (player.JumpInput && player.IsGrounded)
        {
            stateMachine.ChangeState(player.JumpingState);
        }

        if (!player.IsGrounded)
        {
            stateMachine.ChangeState(player.FallingState);
        }
    }

    public override void FixedUpdate()
    {
        // Apply movement
        Vector3 movement = new Vector3(player.MoveInput.x, 0, player.MoveInput.y) * player.moveSpeed;
        player.Rb.linearVelocity = new Vector3(movement.x, player.Rb.linearVelocity.y, movement.z);
    }
}
