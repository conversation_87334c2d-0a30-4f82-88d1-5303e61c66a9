Shader "Custom/CelToon"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color Tint", Color) = (1,1,1,1)
        _Threshold ("Light Threshold", Range(0,1)) = 0.5
        _AmbientLight ("Ambient Light", Range(0,1)) = 0.2
        _ShowTextureOnly ("Show Texture Only", Range(0,1)) = 0
        _DebugNormals ("Debug Normals", Range(0,1)) = 0
        _DebugLighting ("Debug Lighting", Range(0,1)) = 0
        _OutlineColor ("Outline Color", Color) = (0,0,0,1)
        _OutlineWidth ("Outline Width", Range(0.0, 0.1)) = 0.03
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        Pass
        {
            // Outline Pass
            Name "OUTLINE"
            Cull Front
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            float _OutlineWidth;
            float4 _OutlineColor;

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
            };

            v2f vert(appdata v)
            {
                v2f o;
                float3 norm = normalize(v.normal);
                v.vertex.xyz += norm * _OutlineWidth;
                o.pos = UnityObjectToClipPos(v.vertex);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                return _OutlineColor;
            }
            ENDCG
        }

        Pass
        {
            Name "TOON"
            Cull Back
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fwdbase
            #include "UnityCG.cginc"
            #include "Lighting.cginc"

            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _Color;
            float _Threshold;
            float _AmbientLight;
            float _ShowTextureOnly;
            float _DebugNormals;
            float _DebugLighting;

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 normalDir : TEXCOORD1;
            };

            v2f vert(appdata v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                // Sample the texture
                fixed4 texColor = tex2D(_MainTex, i.uv) * _Color;

                // Debug mode: Show texture only (this should ALWAYS work)
                if (_ShowTextureOnly > 0.5) {
                    return texColor;
                }

                // Debug mode: Show normals as colors
                if (_DebugNormals > 0.5) {
                    float3 normalColor = normalize(i.normalDir) * 0.5 + 0.5;
                    return fixed4(normalColor, 1.0);
                }

                // Start with a safe base lighting value
                float lightIntensity = 0.5;

                // Only do lighting calculations if we have valid normal data
                if (length(i.normalDir) > 0.1) {
                    float3 normalDir = normalize(i.normalDir);

                    // Use a simple fixed light direction if no light is available
                    float3 lightDir = float3(0.5, 0.5, -0.5); // Default light direction

                    // If we have a valid light source, use it
                    if (length(_WorldSpaceLightPos0.xyz) > 0.001) {
                        lightDir = normalize(_WorldSpaceLightPos0.xyz);
                    }

                    // Calculate lighting
                    float NdotL = dot(normalDir, lightDir);

                    // Convert from [-1,1] to [0,1] range
                    NdotL = NdotL * 0.5 + 0.5;

                    // Create cel-shaded steps
                    if (NdotL > _Threshold + 0.2)
                        lightIntensity = 1.0;
                    else if (NdotL > _Threshold)
                        lightIntensity = 0.7;
                    else if (NdotL > _Threshold - 0.2)
                        lightIntensity = 0.4;
                    else
                        lightIntensity = max(_AmbientLight, 0.2);
                }

                // Debug mode: Show lighting calculation
                if (_DebugLighting > 0.5) {
                    return fixed4(lightIntensity, lightIntensity, lightIntensity, 1.0);
                }

                // Ensure we never go completely black
                lightIntensity = max(lightIntensity, 0.3);

                // Apply lighting to texture
                fixed3 finalColor = texColor.rgb * lightIntensity;

                return fixed4(finalColor, texColor.a);
            }
            ENDCG
        }
    }
}
