Shader "Custom/CelToon"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color Tint", Color) = (1,1,1,1)
        _Threshold ("Light Threshold", Range(0,1)) = 0.5
        _AmbientLight ("Ambient Light", Range(0,1)) = 0.2
        _ShowTextureOnly ("Show Texture Only", Range(0,1)) = 0
        _DebugNormals ("Debug Normals", Range(0,1)) = 0
        _DebugLighting ("Debug Lighting", Range(0,1)) = 0
        _OutlineColor ("Outline Color", Color) = (0,0,0,1)
        _OutlineWidth ("Outline Width", Range(0.0, 0.1)) = 0.03
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        Pass
        {
            // Outline Pass
            Name "OUTLINE"
            Cull Front
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            float _OutlineWidth;
            float4 _OutlineColor;

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
            };

            v2f vert(appdata v)
            {
                v2f o;
                float3 norm = normalize(v.normal);
                v.vertex.xyz += norm * _OutlineWidth;
                o.pos = UnityObjectToClipPos(v.vertex);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                return _OutlineColor;
            }
            ENDCG
        }

        Pass
        {
            Name "TOON"
            Cull Back
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fwdbase
            #include "UnityCG.cginc"
            #include "Lighting.cginc"

            sampler2D _MainTex;
            float4 _Color;
            float _Threshold;
            float _AmbientLight;
            float _ShowTextureOnly;
            float _DebugNormals;
            float _DebugLighting;

            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 normalDir : TEXCOORD1;
            };

            v2f vert(appdata v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                o.normalDir = UnityObjectToWorldNormal(v.normal);
                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                float4 texColor = tex2D(_MainTex, i.uv) * _Color;
                if (_ShowTextureOnly > 0.5) {
                    return texColor;
                }

                // Debug modes
                if (_DebugNormals > 0.5) {
                    return float4(normalize(i.normalDir) * 0.5 + 0.5, 1.0);
                }

                // Normalize the normal vector
                float3 normalDir = normalize(i.normalDir);

                // Get light direction - handle both directional and point lights
                float3 lightDir;
                if (_WorldSpaceLightPos0.w == 0.0) {
                    // Directional light
                    lightDir = normalize(_WorldSpaceLightPos0.xyz);
                } else {
                    // Point light (though less common in toon shading)
                    lightDir = normalize(_WorldSpaceLightPos0.xyz);
                }

                // Calculate dot product for lighting
                float ndotl = dot(normalDir, lightDir);

                // Remap ndotl from [-1,1] to [0,1] for better lighting
                ndotl = ndotl * 0.5 + 0.5;

                // Create stepped lighting with multiple bands like Telltale style
                float lightIntensity;
                if (ndotl > _Threshold + 0.2)
                    lightIntensity = 1.0;
                else if (ndotl > _Threshold)
                    lightIntensity = 0.7;
                else if (ndotl > _Threshold - 0.2)
                    lightIntensity = 0.4;
                else
                    lightIntensity = _AmbientLight;

                // Debug lighting
                if (_DebugLighting > 0.5) {
                    return float4(lightIntensity, lightIntensity, lightIntensity, 1.0);
                }

                // Ensure minimum visibility of the texture - this is crucial!
                lightIntensity = max(lightIntensity, 0.4);

                // Add some ambient lighting to prevent complete darkness
                float3 ambient = UNITY_LIGHTMODEL_AMBIENT.rgb * 0.5;

                // Combine texture color with lighting
                float3 finalColor = texColor.rgb * lightIntensity + ambient * texColor.rgb;

                return float4(finalColor, texColor.a);
            }
            ENDCG
        }
    }
}
