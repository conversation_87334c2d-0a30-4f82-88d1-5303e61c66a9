fileFormatVersion: 2
guid: a2ec695bf98583344bdd1daca5ca2501
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: Ch10_body
    second: {fileID: 2100000, guid: 5eb556dd8ea22ef44992d8199e79d0df, type: 2}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: idle
      takeName: mixamo.com
      internalID: -203655887218126122
      firstFrame: 0
      lastFrame: 127
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: mixamorig5:Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig5:Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Zombie Male Shirtless(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Ch10
      parentName: Zombie Male Shirtless(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig5:Hips
      parentName: Zombie Male Shirtless(Clone)
      position: {x: 0.0013469869, y: 1.0791286, z: -0.03220975}
      rotation: {x: -2.1175825e-17, y: 0.000000005820766, z: -7.2759576e-10, w: 1}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: mixamorig5:Spine
      parentName: mixamorig5:Hips
      position: {x: -0, y: 0.08836052, z: -0.01360826}
      rotation: {x: -0.07632964, y: -0.0000000059148593, z: 0.0000000010066492, w: 0.99708265}
      scale: {x: 0.99999994, y: 1, z: 1}
    - name: mixamorig5:Spine1
      parentName: mixamorig5:Spine
      position: {x: -0, y: 0.10430254, z: -6.330186e-10}
      rotation: {x: -0.000000067055225, y: -0, z: -0, w: 1}
      scale: {x: 1.0000001, y: 0.9999999, z: 0.99999994}
    - name: mixamorig5:Spine2
      parentName: mixamorig5:Spine1
      position: {x: -0, y: 0.119202875, z: -2.406523e-10}
      rotation: {x: 0.000000029802322, y: 0.0000000060851906, z: -0.0000000012713504, w: 1}
      scale: {x: 1.0000001, y: 1, z: 0.99999994}
    - name: mixamorig5:Neck
      parentName: mixamorig5:Spine2
      position: {x: -0, y: 0.13410336, z: 0.00000002594304}
      rotation: {x: 0.07632968, y: -0.0000000058870904, z: 6.439126e-10, w: 0.99708265}
      scale: {x: 1, y: 1.0000001, z: 1.0000001}
    - name: mixamorig5:Head
      parentName: mixamorig5:Neck
      position: {x: -0, y: 0.06391144, z: 0.032174524}
      rotation: {x: -0, y: -0, z: -1.8189905e-10, w: 1}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: mixamorig5:HeadTop_End
      parentName: mixamorig5:Head
      position: {x: -0, y: 0.1978102, z: 0.099582344}
      rotation: {x: -0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 0.9999999}
    - name: mixamorig5:LeftShoulder
      parentName: mixamorig5:Spine2
      position: {x: -0.06322326, y: 0.11894753, z: -0.0025803843}
      rotation: {x: 0.54607254, y: -0.44391277, z: 0.5694227, w: 0.4248577}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000005}
    - name: mixamorig5:LeftArm
      parentName: mixamorig5:LeftShoulder
      position: {x: 1.1914771e-11, y: 0.12863727, z: -9.313766e-11}
      rotation: {x: -0.08324195, y: 0.00089275825, z: -0.014699636, w: 0.99642056}
      scale: {x: 1.0000005, y: 1.0000005, z: 1.0000004}
    - name: mixamorig5:LeftForeArm
      parentName: mixamorig5:LeftArm
      position: {x: -4.7597837e-12, y: 0.26918954, z: -1.7974172e-11}
      rotation: {x: -0.06505015, y: 0.0030105119, z: -0.039466266, w: 0.9970968}
      scale: {x: 1.0000001, y: 1.0000001, z: 1.0000002}
    - name: mixamorig5:LeftHand
      parentName: mixamorig5:LeftForeArm
      position: {x: -2.1943869e-11, y: 0.2478951, z: 4.9434534e-10}
      rotation: {x: 0.059461225, y: -0.12840119, z: -0.009960362, w: 0.9898881}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
    - name: mixamorig5:LeftHandThumb1
      parentName: mixamorig5:LeftHand
      position: {x: 0.02868926, y: 0.034067553, z: 0.016066138}
      rotation: {x: 0.044238526, y: 0.017068597, z: -0.34491497, w: 0.9374355}
      scale: {x: 1.0000004, y: 0.99999994, z: 1.0000006}
    - name: mixamorig5:LeftHandThumb2
      parentName: mixamorig5:LeftHandThumb1
      position: {x: 0.0046146116, y: 0.037595883, z: 4.779389e-10}
      rotation: {x: 0.03087258, y: 0.002668359, z: -0.03217519, w: 0.99900174}
      scale: {x: 1, y: 0.99999994, z: 0.99999994}
    - name: mixamorig5:LeftHandThumb3
      parentName: mixamorig5:LeftHandThumb2
      position: {x: 0.00028810417, y: 0.031414445, z: -0.0000000023348237}
      rotation: {x: -0.01741276, y: 0.0038346024, z: -0.0754876, w: 0.99698734}
      scale: {x: 1, y: 1.0000002, z: 1.0000001}
    - name: mixamorig5:LeftHandThumb4
      parentName: mixamorig5:LeftHandThumb3
      position: {x: -0.0049027125, y: 0.023127262, z: -2.8387234e-10}
      rotation: {x: -0, y: -0, z: 3.88578e-16, w: 1}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000004}
    - name: mixamorig5:LeftHandIndex1
      parentName: mixamorig5:LeftHand
      position: {x: 0.03539693, y: 0.102416694, z: -0.0013483461}
      rotation: {x: 0.07369934, y: -0.004648311, z: -0.05979735, w: 0.9954753}
      scale: {x: 0.99999994, y: 1.0000005, z: 1.0000005}
    - name: mixamorig5:LeftHandIndex2
      parentName: mixamorig5:LeftHandIndex1
      position: {x: -0.00003912126, y: 0.030230278, z: -9.605676e-10}
      rotation: {x: -0.0018995327, y: -0.00000007823109, z: -0.00000003757304, w: 0.9999982}
      scale: {x: 1, y: 1.0000005, z: 1.0000005}
    - name: mixamorig5:LeftHandIndex3
      parentName: mixamorig5:LeftHandIndex2
      position: {x: -0.000046420675, y: 0.027880725, z: 6.7853617e-10}
      rotation: {x: 0.030549323, y: 0.000000038300556, z: 0.000000033550386, w: 0.99953324}
      scale: {x: 0.99999994, y: 0.9999997, z: 0.9999998}
    - name: mixamorig5:LeftHandIndex4
      parentName: mixamorig5:LeftHandIndex3
      position: {x: 0.00008554126, y: 0.02465366, z: -2.1468054e-10}
      rotation: {x: -0, y: -0, z: 1.6653343e-15, w: 1}
      scale: {x: 1, y: 1.0000006, z: 1.0000005}
    - name: mixamorig5:LeftHandMiddle1
      parentName: mixamorig5:LeftHand
      position: {x: 0.010724826, y: 0.10124888, z: -0.0016745983}
      rotation: {x: 0.022785723, y: 0.0008486654, z: 0.021290118, w: 0.99951327}
      scale: {x: 0.99999994, y: 1.0000005, z: 1.0000005}
    - name: mixamorig5:LeftHandMiddle2
      parentName: mixamorig5:LeftHandMiddle1
      position: {x: -0.00017970747, y: 0.033990297, z: -2.3617075e-10}
      rotation: {x: 0.039253972, y: -0.00003661103, z: 0.0024556902, w: 0.9992262}
      scale: {x: 1, y: 1.0000008, z: 1.0000007}
    - name: mixamorig5:LeftHandMiddle3
      parentName: mixamorig5:LeftHandMiddle2
      position: {x: 0.00012315287, y: 0.03301149, z: 8.2511714e-10}
      rotation: {x: -0.017330637, y: -0.000000015967382, z: -0.000000028283916, w: 0.9998498}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: mixamorig5:LeftHandMiddle4
      parentName: mixamorig5:LeftHandMiddle3
      position: {x: 0.00005655469, y: 0.029507302, z: -0.000000001080939}
      rotation: {x: 0.0000000018626449, y: -0, z: -5.273559e-16, w: 1}
      scale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
    - name: mixamorig5:LeftHandRing1
      parentName: mixamorig5:LeftHand
      position: {x: -0.012615826, y: 0.10227328, z: -0.0024396153}
      rotation: {x: 0.04657463, y: 0.008627464, z: 0.005332119, w: 0.9988634}
      scale: {x: 0.99999994, y: 1.0000006, z: 1.0000005}
    - name: mixamorig5:LeftHandRing2
      parentName: mixamorig5:LeftHandRing1
      position: {x: 0.000012785049, y: 0.029767798, z: -7.216968e-10}
      rotation: {x: 0.02530697, y: -0.00000006426126, z: -0.00000010820804, w: 0.9996798}
      scale: {x: 1.0000004, y: 1.0000007, z: 1.0000008}
    - name: mixamorig5:LeftHandRing3
      parentName: mixamorig5:LeftHandRing2
      position: {x: 0.00006702065, y: 0.027034886, z: -5.3270808e-11}
      rotation: {x: -0.03996372, y: 0.0000146035845, z: -0.0013136795, w: 0.9992003}
      scale: {x: 0.99999994, y: 0.9999998, z: 1.0000001}
    - name: mixamorig5:LeftHandRing4
      parentName: mixamorig5:LeftHandRing3
      position: {x: -0.00007980593, y: 0.023656556, z: -4.7974336e-10}
      rotation: {x: -0, y: -0, z: -4.884981e-15, w: 1}
      scale: {x: 1.0000001, y: 1, z: 0.99999994}
    - name: mixamorig5:LeftHandPinky1
      parentName: mixamorig5:LeftHand
      position: {x: -0.03350594, y: 0.09005168, z: -0.002913688}
      rotation: {x: 0.03426483, y: 0.025894612, z: 0.00886195, w: 0.999038}
      scale: {x: 1, y: 1.0000007, z: 1.0000006}
    - name: mixamorig5:LeftHandPinky2
      parentName: mixamorig5:LeftHandPinky1
      position: {x: -0.000008966486, y: 0.028002795, z: 7.1940204e-10}
      rotation: {x: 0.019602353, y: -0.000000089406946, z: -0.0000000037252894, w: 0.9998079}
      scale: {x: 0.99999994, y: 1.0000002, z: 1.0000001}
    - name: mixamorig5:LeftHandPinky3
      parentName: mixamorig5:LeftHandPinky2
      position: {x: 0.000005941636, y: 0.023067499, z: -3.349527e-11}
      rotation: {x: -0.039932624, y: -0.000007069356, z: -0.00002294631, w: 0.9992024}
      scale: {x: 1.0000002, y: 1.0000006, z: 1.0000006}
    - name: mixamorig5:LeftHandPinky4
      parentName: mixamorig5:LeftHandPinky3
      position: {x: 0.0000030242009, y: 0.019274905, z: -3.4241338e-10}
      rotation: {x: -0, y: -0, z: -8.881782e-16, w: 1}
      scale: {x: 1.0000001, y: 1.0000005, z: 1.0000004}
    - name: mixamorig5:RightShoulder
      parentName: mixamorig5:Spine2
      position: {x: 0.06322326, y: 0.11887343, z: -0.0020992393}
      rotation: {x: -0.54850227, y: -0.44192243, z: 0.5675905, w: -0.4262523}
      scale: {x: 1, y: 1, z: 1.0000002}
    - name: mixamorig5:RightArm
      parentName: mixamorig5:RightShoulder
      position: {x: -1.7378737e-11, y: 0.12863727, z: -9.227477e-11}
      rotation: {x: 0.08395233, y: 0.00044097, z: -0.010431615, w: -0.99641514}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000005}
    - name: mixamorig5:RightForeArm
      parentName: mixamorig5:RightArm
      position: {x: -1.7864994e-10, y: 0.26919058, z: -0.0000000011895249}
      rotation: {x: -0.060656607, y: -0.0037965563, z: 0.043622214, w: 0.9971978}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000004}
    - name: mixamorig5:RightHand
      parentName: mixamorig5:RightForeArm
      position: {x: -0.0000000011179129, y: 0.24821201, z: -1.4890304e-10}
      rotation: {x: 0.059222426, y: 0.11353242, z: -0.024899514, w: 0.991455}
      scale: {x: 1.0000002, y: 1, z: 0.9999997}
    - name: mixamorig5:RightHandThumb1
      parentName: mixamorig5:RightHand
      position: {x: -0.026898516, y: 0.031962443, z: 0.016248502}
      rotation: {x: 0.04866878, y: -0.013002649, z: 0.36236718, w: 0.9306731}
      scale: {x: 1.0000001, y: 1.0000002, z: 1}
    - name: mixamorig5:RightHandThumb2
      parentName: mixamorig5:RightHandThumb1
      position: {x: -0.0033076333, y: 0.03570595, z: 3.778723e-10}
      rotation: {x: 0.031261604, y: -0.0016770752, z: 0.037651185, w: 0.9988005}
      scale: {x: 1, y: 1.0000006, z: 1.0000002}
    - name: mixamorig5:RightHandThumb3
      parentName: mixamorig5:RightHandThumb2
      position: {x: 0.00088053185, y: 0.031333685, z: -9.873516e-10}
      rotation: {x: -0.036482044, y: -0.0046747304, z: 0.02320724, w: 0.9990539}
      scale: {x: 1.0000002, y: 1.0000005, z: 1}
    - name: mixamorig5:RightHandThumb4
      parentName: mixamorig5:RightHandThumb3
      position: {x: 0.002427104, y: 0.024946377, z: 6.382984e-10}
      rotation: {x: -0, y: 3.5527133e-15, z: 4.4408916e-16, w: 1}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000005}
    - name: mixamorig5:RightHandIndex1
      parentName: mixamorig5:RightHand
      position: {x: -0.03465696, y: 0.10104245, z: -0.001317044}
      rotation: {x: 0.06350492, y: 0.0060421736, z: 0.077330045, w: 0.99496263}
      scale: {x: 1.0000001, y: 0.9999998, z: 1}
    - name: mixamorig5:RightHandIndex2
      parentName: mixamorig5:RightHandIndex1
      position: {x: -0.00000442485, y: 0.030936396, z: 0.0000000016890186}
      rotation: {x: 0.008686027, y: 0.000000025611365, z: 0.00000002869637, w: 0.9999623}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000002}
    - name: mixamorig5:RightHandIndex3
      parentName: mixamorig5:RightHandIndex2
      position: {x: -0.000025047317, y: 0.028265284, z: 0.000000001261964}
      rotation: {x: -0.023932677, y: -0.000000027816045, z: -0.000000065558154, w: 0.9997136}
      scale: {x: 0.9999998, y: 0.9999998, z: 1}
    - name: mixamorig5:RightHandIndex4
      parentName: mixamorig5:RightHandIndex3
      position: {x: 0.000029472447, y: 0.02474862, z: -3.213168e-10}
      rotation: {x: -0, y: -0, z: 1.998401e-15, w: 1}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: mixamorig5:RightHandMiddle1
      parentName: mixamorig5:RightHand
      position: {x: -0.011434262, y: 0.101464614, z: -0.0030966955}
      rotation: {x: 0.04027619, y: -0.0017310528, z: -0.004272617, w: 0.999178}
      scale: {x: 1, y: 1.0000002, z: 1}
    - name: mixamorig5:RightHandMiddle2
      parentName: mixamorig5:RightHandMiddle1
      position: {x: 0.000015930842, y: 0.034091394, z: -2.7390398e-10}
      rotation: {x: 0.03958673, y: -0.000000018975696, z: 0.000000025494952, w: 0.99921614}
      scale: {x: 1.0000007, y: 1.0000011, z: 1.0000006}
    - name: mixamorig5:RightHandMiddle3
      parentName: mixamorig5:RightHandMiddle2
      position: {x: -0.0000059145787, y: 0.032456942, z: -8.535687e-10}
      rotation: {x: 0.0103529645, y: 0.0000000319008, z: 0.00000007377508, w: 0.9999464}
      scale: {x: 1, y: 0.9999998, z: 0.99999994}
    - name: mixamorig5:RightHandMiddle4
      parentName: mixamorig5:RightHandMiddle3
      position: {x: -0.000010016255, y: 0.029375877, z: 6.443096e-10}
      rotation: {x: -9.3132246e-10, y: -0, z: -5.273559e-16, w: 1}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: mixamorig5:RightHandRing1
      parentName: mixamorig5:RightHand
      position: {x: 0.012470116, y: 0.10300536, z: 0.00020169227}
      rotation: {x: 0.01356673, y: -0.000986099, z: -0.020745436, w: 0.99969226}
      scale: {x: 1.0000002, y: 1.0000001, z: 1}
    - name: mixamorig5:RightHandRing2
      parentName: mixamorig5:RightHandRing1
      position: {x: 0.0000035699697, y: 0.028929548, z: -2.652021e-10}
      rotation: {x: 0.03787524, y: -0.0000031591621, z: -0.00010869942, w: 0.99928254}
      scale: {x: 1.0000007, y: 1.0000007, z: 1.0000006}
    - name: mixamorig5:RightHandRing3
      parentName: mixamorig5:RightHandRing2
      position: {x: -0.000005624501, y: 0.02798451, z: -8.051029e-10}
      rotation: {x: -0.038407996, y: 0.0000035204716, z: 0.00009286521, w: 0.9992622}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000001}
    - name: mixamorig5:RightHandRing4
      parentName: mixamorig5:RightHandRing3
      position: {x: 0.0000020545522, y: 0.022116078, z: 3.3537587e-10}
      rotation: {x: -0.000000007450579, y: -7.1054257e-15, z: 1.7763564e-15, w: 1}
      scale: {x: 1.0000002, y: 1.0000001, z: 1.0000005}
    - name: mixamorig5:RightHandPinky1
      parentName: mixamorig5:RightHand
      position: {x: 0.033621106, y: 0.093650185, z: -0.0016871952}
      rotation: {x: -0.013583529, y: 0.012228663, z: 0.015197458, w: -0.9997175}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
    - name: mixamorig5:RightHandPinky2
      parentName: mixamorig5:RightHandPinky1
      position: {x: -0.00006103087, y: 0.027904011, z: -6.990007e-10}
      rotation: {x: 0.03917641, y: 0.000042784955, z: 0.0017009098, w: 0.99923086}
      scale: {x: 1, y: 1.0000005, z: 1.0000001}
    - name: mixamorig5:RightHandPinky3
      parentName: mixamorig5:RightHandPinky2
      position: {x: 0.00008750677, y: 0.022319786, z: -5.986757e-10}
      rotation: {x: -0.040293135, y: -0.000031762185, z: -0.0010391066, w: 0.99918735}
      scale: {x: 1.0000002, y: 1.0000004, z: 1.0000004}
    - name: mixamorig5:RightHandPinky4
      parentName: mixamorig5:RightHandPinky3
      position: {x: -0.00002647484, y: 0.018398363, z: -2.9376734e-10}
      rotation: {x: -0, y: -0, z: -0, w: 1}
      scale: {x: 1.0000004, y: 1.0000004, z: 1.0000001}
    - name: mixamorig5:LeftUpLeg
      parentName: mixamorig5:Hips
      position: {x: -0.09373935, y: -0.04910606, z: -0.0052553043}
      rotation: {x: 0.00061663066, y: -0.01663007, z: 0.9991749, w: 0.037049063}
      scale: {x: 1.0000001, y: 1.0000002, z: 0.9999999}
    - name: mixamorig5:LeftLeg
      parentName: mixamorig5:LeftUpLeg
      position: {x: -0.0000000011392183, y: 0.4328691, z: 3.303893e-10}
      rotation: {x: 0.0019546144, y: -0.000025620797, z: -0.013100235, w: 0.99991226}
      scale: {x: 1.0000005, y: 1.0000005, z: 1.0000001}
    - name: mixamorig5:LeftFoot
      parentName: mixamorig5:LeftLeg
      position: {x: -0.0000000020957416, y: 0.45713344, z: 6.3014033e-10}
      rotation: {x: 0.4421718, y: 0.010003214, z: -0.0049318145, w: 0.8968611}
      scale: {x: 1.0000004, y: 1, z: 1}
    - name: mixamorig5:LeftToeBase
      parentName: mixamorig5:LeftFoot
      position: {x: 4.223626e-11, y: 0.18168738, z: -0.000000003229727}
      rotation: {x: 0.3156844, y: 0.0747287, z: -0.024948137, w: 0.94558793}
      scale: {x: 0.9999998, y: 1.0000001, z: 0.9999996}
    - name: mixamorig5:LeftToe_End
      parentName: mixamorig5:LeftToeBase
      position: {x: 1.0749659e-10, y: 0.06899148, z: 4.3854587e-11}
      rotation: {x: -0, y: -0, z: -0, w: 1}
      scale: {x: 1.0000005, y: 1.0000002, z: 1.0000002}
    - name: mixamorig5:RightUpLeg
      parentName: mixamorig5:Hips
      position: {x: 0.09373935, y: -0.04910606, z: -0.0054134238}
      rotation: {x: -0.0007112993, y: -0.019186392, z: 0.99912935, w: -0.037040614}
      scale: {x: 0.9999998, y: 0.9999999, z: 1}
    - name: mixamorig5:RightLeg
      parentName: mixamorig5:RightUpLeg
      position: {x: -2.2282534e-10, y: 0.4329481, z: 1.7970612e-10}
      rotation: {x: 0.0041497094, y: 0.000054412638, z: 0.013107162, w: 0.9999055}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: mixamorig5:RightFoot
      parentName: mixamorig5:RightLeg
      position: {x: 0.0000000014077974, y: 0.4571431, z: 1.8684403e-10}
      rotation: {x: 0.44579247, y: -0.010084119, z: 0.0050224713, w: 0.8950654}
      scale: {x: 1.0000004, y: 1.0000001, z: 1.0000001}
    - name: mixamorig5:RightToeBase
      parentName: mixamorig5:RightFoot
      position: {x: 8.5881857e-10, y: 0.18335022, z: -0.0000000021652709}
      rotation: {x: 0.3121431, y: -0.076281235, z: 0.025152963, w: 0.9466336}
      scale: {x: 1.000001, y: 1.0000004, z: 1.0000005}
    - name: mixamorig5:RightToe_End
      parentName: mixamorig5:RightToeBase
      position: {x: -9.050964e-12, y: 0.0686321, z: 8.691847e-11}
      rotation: {x: -0.000000029802322, y: -0, z: -0.0000000018626451, w: 1}
      scale: {x: 0.9999998, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
