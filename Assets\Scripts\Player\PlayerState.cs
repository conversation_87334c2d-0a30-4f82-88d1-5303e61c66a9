using UnityEngine;

public abstract class PlayerState
{
    protected PlayerMovement player;
    protected PlayerStateMachine stateMachine;

    public PlayerState(PlayerMovement player, PlayerStateMachine stateMachine)
    {
        this.player = player;
        this.stateMachine = stateMachine;
    }

    public virtual void Enter()
    {
        // Called when entering this state
    }

    public virtual void Update()
    {
        // Called every frame while in this state
    }

    public virtual void FixedUpdate()
    {
        // Called every physics frame while in this state
    }

    public virtual void Exit()
    {
        // Called when exiting this state
    }
}
