using UnityEngine;

public class JumpingState : PlayerState
{
    private float jumpTime;
    private float maxJumpTime = 0.2f; // How long the jump boost lasts

    public JumpingState(PlayerMovement player, PlayerStateMachine stateMachine) : base(player, stateMachine)
    {
    }

    public override void Enter()
    {
        Debug.Log("Entering Jumping State");
        // Apply jump force
        player.Rb.linearVelocity = new Vector3(player.Rb.linearVelocity.x, player.jumpStrength, player.Rb.linearVelocity.z);
        jumpTime = 0f;
    }

    public override void Update()
    {
        jumpTime += Time.deltaTime;

        // Allow air control during jump
        if (player.MoveInput.magnitude > 0.1f)
        {
            Vector3 airMovement = new Vector3(player.MoveInput.x, 0, player.MoveInput.y) * player.moveSpeed * 0.5f; // Reduced air control
            player.Rb.linearVelocity = new Vector3(airMovement.x, player.Rb.linearVelocity.y, airMovement.z);
        }

        // Transition to falling when jump time is over or when falling
        if (jumpTime > maxJumpTime || player.Rb.linearVelocity.y <= 0)
        {
            stateMachine.ChangeState(player.FallingState);
        }
    }
}
