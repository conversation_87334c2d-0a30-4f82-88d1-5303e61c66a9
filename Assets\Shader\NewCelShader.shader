Shader "Custom/NewCelShader"
{
    Properties
    {
        _MainTex ("Diffuse Texture", 2D) = "white" {}
        _Color ("Color Tint", Color) = (1,1,1,1)
        _CelLevels ("Cel Levels", Range(1,10)) = 3
        _CelThreshold ("Cel Threshold", Range(0,1)) = 0.5
        _RimColor ("Rim Color", Color) = (1,1,1,1)
        _RimPower ("Rim Power", Range(0.1,8)) = 3
    }
    
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 200
        
        Pass
        {
            Tags { "LightMode"="ForwardBase" }
            
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_fwdbase
            
            #include "UnityCG.cginc"
            #include "Lighting.cginc"
            #include "AutoLight.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 worldNormal : TEXCOORD1;
                float3 worldPos : TEXCOORD2;
                float3 viewDir : TEXCOORD3;
                SHADOW_COORDS(4)
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _Color;
            float _CelLevels;
            float _CelThreshold;
            float4 _RimColor;
            float _RimPower;
            
            v2f vert(appdata v)
            {
                v2f o;
                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldNormal = UnityObjectToWorldNormal(v.normal);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.viewDir = normalize(UnityWorldSpaceViewDir(o.worldPos));
                TRANSFER_SHADOW(o);
                return o;
            }
            
            float4 frag(v2f i) : SV_Target
            {
                // Sample diffuse texture
                float4 texColor = tex2D(_MainTex, i.uv) * _Color;
                
                // Normalize vectors
                float3 normal = normalize(i.worldNormal);
                float3 lightDir = normalize(_WorldSpaceLightPos0.xyz);
                float3 viewDir = normalize(i.viewDir);
                
                // Basic diffuse lighting
                float NdotL = dot(normal, lightDir);
                NdotL = max(0, NdotL); // Clamp to positive values
                
                // Create cel-shaded steps
                float celShade = floor(NdotL * _CelLevels) / _CelLevels;
                celShade = max(celShade, 0.2); // Ensure minimum brightness
                
                // Apply shadow attenuation
                float shadow = SHADOW_ATTENUATION(i);
                celShade *= shadow;
                
                // Rim lighting for that cartoon pop
                float rim = 1.0 - saturate(dot(viewDir, normal));
                rim = pow(rim, _RimPower);
                
                // Combine everything
                float3 finalColor = texColor.rgb * celShade * _LightColor0.rgb;
                finalColor += _RimColor.rgb * rim * 0.5;
                
                // Add some ambient to prevent pure black
                finalColor += texColor.rgb * 0.2;
                
                return float4(finalColor, texColor.a);
            }
            ENDCG
        }
    }
    
    // Fallback for older hardware
    Fallback "Diffuse"
}
