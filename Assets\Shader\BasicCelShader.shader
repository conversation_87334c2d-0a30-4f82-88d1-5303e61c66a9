Shader "Custom/BasicCelShader"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color", Color) = (1,1,1,1)
        _Steps ("Lighting Steps", Range(2,10)) = 4
        _TestMode ("Test Mode", Range(0,3)) = 0
    }
    
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 worldNormal : TEXCOORD1;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _Color;
            float _Steps;
            float _TestMode;
            
            v2f vert(appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldNormal = UnityObjectToWorldNormal(v.normal);
                return o;
            }
            
            fixed4 frag(v2f i) : SV_Target
            {
                // Get texture color
                fixed4 texColor = tex2D(_MainTex, i.uv) * _Color;
                
                // Test Mode 0: Just show texture (like SimpleCelShader)
                if (_TestMode < 0.5) {
                    return texColor;
                }
                
                // Test Mode 1: Show normals as colors
                if (_TestMode < 1.5) {
                    float3 normalColor = normalize(i.worldNormal) * 0.5 + 0.5;
                    return fixed4(normalColor, 1);
                }
                
                // Test Mode 2: Show basic lighting (no cel shading yet)
                if (_TestMode < 2.5) {
                    float3 lightDir = float3(0.5, 0.5, -0.5); // Fixed light direction
                    float3 normal = normalize(i.worldNormal);
                    float lighting = max(0, dot(normal, lightDir));
                    return texColor * lighting + texColor * 0.3; // Add ambient
                }
                
                // Test Mode 3: Cel-shaded lighting
                float3 lightDir = float3(0.5, 0.5, -0.5); // Fixed light direction
                float3 normal = normalize(i.worldNormal);
                float lighting = max(0, dot(normal, lightDir));
                
                // Create cel-shaded steps
                lighting = floor(lighting * _Steps) / _Steps;
                lighting = max(lighting, 0.3); // Minimum brightness
                
                return texColor * lighting;
            }
            ENDCG
        }
    }
}
