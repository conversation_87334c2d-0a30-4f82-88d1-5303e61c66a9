Shader "Custom/BasicCelShader"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color", Color) = (1,1,1,1)
        _Steps ("Lighting Steps", Range(2,10)) = 4
        _ShadowThreshold ("Shadow Threshold", Range(0,1)) = 0.5
        _AmbientStrength ("Ambient Strength", Range(0,1)) = 0.3
        _RimColor ("Rim Color", Color) = (1,1,1,0.5)
        _RimPower ("Rim Power", Range(0.1,8)) = 2
        _UseSceneLighting ("Use Scene Lighting", Range(0,1)) = 1

        [Header(Cartoon Stylization)]
        _Saturation ("Saturation Boost", Range(0,3)) = 1.2
        _Contrast ("Contrast", Range(0,3)) = 1.1
        _Brightness ("Brightness", Range(0,2)) = 1.0
        _Posterize ("Posterize Colors", Range(2,32)) = 16
        _UsePosterize ("Use Posterization", Range(0,1)) = 0

        _TestMode ("Test Mode", Range(0,5)) = 4
    }
    
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100
        
        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            
            struct appdata
            {
                float4 vertex : POSITION;
                float3 normal : NORMAL;
                float2 uv : TEXCOORD0;
            };
            
            struct v2f
            {
                float4 vertex : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 worldNormal : TEXCOORD1;
                float3 worldPos : TEXCOORD2;
                float3 viewDir : TEXCOORD3;
            };
            
            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _Color;
            float _Steps;
            float _ShadowThreshold;
            float _AmbientStrength;
            float4 _RimColor;
            float _RimPower;
            float _UseSceneLighting;
            float _Saturation;
            float _Contrast;
            float _Brightness;
            float _Posterize;
            float _UsePosterize;
            float _TestMode;
            
            v2f vert(appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldNormal = UnityObjectToWorldNormal(v.normal);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                o.viewDir = normalize(UnityWorldSpaceViewDir(o.worldPos));
                return o;
            }
            
            // Cartoon stylization functions
            float3 adjustSaturation(float3 color, float saturation) {
                float3 grayscale = dot(color, float3(0.299, 0.587, 0.114));
                return lerp(grayscale, color, saturation);
            }

            float3 adjustContrast(float3 color, float contrast) {
                return saturate((color - 0.5) * contrast + 0.5);
            }

            float3 posterizeColor(float3 color, float levels) {
                return floor(color * levels) / levels;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                // Get texture color
                fixed4 texColor = tex2D(_MainTex, i.uv) * _Color;

                // Apply cartoon stylization to the base texture
                float3 stylizedColor = texColor.rgb;

                // Brightness adjustment
                stylizedColor *= _Brightness;

                // Contrast adjustment
                stylizedColor = adjustContrast(stylizedColor, _Contrast);

                // Saturation boost for more vibrant cartoon look
                stylizedColor = adjustSaturation(stylizedColor, _Saturation);

                // Optional posterization for more cartoon-like color banding
                if (_UsePosterize > 0.5) {
                    stylizedColor = posterizeColor(stylizedColor, _Posterize);
                }

                // Update texColor with stylized version
                texColor.rgb = stylizedColor;

                // Test Mode 0: Original texture (no stylization)
                if (_TestMode < 0.5) {
                    return tex2D(_MainTex, i.uv) * _Color; // Original, unstylized
                }

                // Test Mode 5: Show stylized texture only (no lighting)
                if (_TestMode > 4.5) {
                    return texColor; // Stylized texture without lighting
                }

                // Test Mode 1: Show normals as colors
                if (_TestMode < 1.5) {
                    float3 normalColor = normalize(i.worldNormal) * 0.5 + 0.5;
                    return fixed4(normalColor, 1);
                }

                // Prepare lighting vectors
                float3 normal = normalize(i.worldNormal);
                float3 viewDir = normalize(i.viewDir);

                // Choose light direction
                float3 lightDir = float3(0.5, 0.5, -0.5); // Default fixed light
                if (_UseSceneLighting > 0.5 && length(_WorldSpaceLightPos0.xyz) > 0.001) {
                    lightDir = normalize(_WorldSpaceLightPos0.xyz);
                }

                // Calculate basic lighting
                float NdotL = dot(normal, lightDir);
                float lighting = max(0, NdotL);

                // Test Mode 2: Show smooth lighting
                if (_TestMode < 2.5) {
                    return texColor * lighting + texColor * _AmbientStrength;
                }

                // Test Mode 3: Basic cel-shading
                if (_TestMode < 3.5) {
                    lighting = floor(lighting * _Steps) / _Steps;
                    lighting = max(lighting, _AmbientStrength);
                    return texColor * lighting;
                }

                // Test Mode 4: Advanced cel-shading with rim lighting (Telltale style)

                // Telltale-style stepped lighting with specific thresholds
                float celLighting;
                if (lighting > _ShadowThreshold + 0.3)
                    celLighting = 1.0;           // Full light
                else if (lighting > _ShadowThreshold + 0.1)
                    celLighting = 0.7;           // Mid light
                else if (lighting > _ShadowThreshold - 0.1)
                    celLighting = 0.4;           // Soft shadow
                else
                    celLighting = _AmbientStrength; // Deep shadow

                // Rim lighting for that cartoon pop
                float rim = 1.0 - saturate(dot(viewDir, normal));
                rim = pow(rim, _RimPower);
                float3 rimLighting = _RimColor.rgb * rim * _RimColor.a;

                // Combine diffuse and rim lighting
                float3 finalColor = texColor.rgb * celLighting + rimLighting;

                return fixed4(finalColor, texColor.a);
            }
            ENDCG
        }
    }
}
