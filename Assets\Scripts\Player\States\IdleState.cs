using UnityEngine;

public class IdleState : PlayerState
{
    public IdleState(PlayerMovement player, PlayerStateMachine stateMachine) : base(player, stateMachine)
    {
    }

    public override void Enter()
    {
        Debug.Log("Entering Idle State");
    }

    public override void Update()
    {
        // Check for state transitions
        if (player.MoveInput.magnitude > 0.1f)
        {
            if (player.IsGrounded)
            {
                stateMachine.ChangeState(player.MovingState);
            }
        }

        if (player.JumpInput && player.IsGrounded)
        {
            stateMachine.ChangeState(player.JumpingState);
        }

        if (!player.IsGrounded)
        {
            stateMachine.ChangeState(player.FallingState);
        }
    }

    public override void FixedUpdate()
    {
        // Apply slight deceleration when idle
        Vector3 velocity = player.Rb.linearVelocity;
        velocity.x = Mathf.Lerp(velocity.x, 0, Time.fixedDeltaTime * 10f);
        velocity.z = Mathf.Lerp(velocity.z, 0, Time.fixedDeltaTime * 10f);
        player.Rb.linearVelocity = velocity;
    }
}
