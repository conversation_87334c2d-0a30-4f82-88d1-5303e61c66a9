Shader "Custom/DiagnosticShader"
{
    Properties
    {
        _MainTex ("Texture", 2D) = "white" {}
        _Color ("Color", Color) = (1,1,1,1)
        _TestMode ("Test Mode", Range(0,4)) = 0
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        LOD 100

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                float3 normal : NORMAL;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 worldNormal : TEXCOORD1;
                float3 worldPos : TEXCOORD2;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _Color;
            float _TestMode;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldNormal = UnityObjectToWorldNormal(v.normal);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                if (_TestMode < 0.5) {
                    // Test 0: Pure red color
                    return fixed4(1, 0, 0, 1);
                }
                else if (_TestMode < 1.5) {
                    // Test 1: Show texture only
                    return tex2D(_MainTex, i.uv) * _Color;
                }
                else if (_TestMode < 2.5) {
                    // Test 2: Show normals as colors
                    return fixed4(normalize(i.worldNormal) * 0.5 + 0.5, 1);
                }
                else if (_TestMode < 3.5) {
                    // Test 3: Show UV coordinates
                    return fixed4(i.uv, 0, 1);
                }
                else {
                    // Test 4: Show world position as color
                    return fixed4(frac(i.worldPos * 0.1), 1);
                }
            }
            ENDCG
        }
    }
}
