using UnityEngine;

[System.Serializable]
public class SceneLightingSetup : MonoBehaviour
{
    [Header("Auto Setup Lighting")]
    public bool autoSetupOnStart = true;
    
    [Header("Directional Light Settings")]
    public Color lightColor = Color.white;
    public float lightIntensity = 1.0f;
    public Vector3 lightRotation = new Vector3(50f, -30f, 0f);
    
    [Header("Ambient Settings")]
    public Color ambientColor = new Color(0.3f, 0.3f, 0.4f, 1f);
    public float ambientIntensity = 0.5f;
    
    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupSceneLighting();
        }
    }
    
    [ContextMenu("Setup Scene Lighting")]
    public void SetupSceneLighting()
    {
        // Find or create directional light
        Light directionalLight = RenderSettings.sun;
        
        if (directionalLight == null)
        {
            // Look for any directional light in the scene
            Light[] lights = FindObjectsOfType<Light>();
            foreach (Light light in lights)
            {
                if (light.type == LightType.Directional)
                {
                    directionalLight = light;
                    break;
                }
            }
        }
        
        if (directionalLight == null)
        {
            // Create a new directional light
            GameObject lightGO = new GameObject("Directional Light");
            directionalLight = lightGO.AddComponent<Light>();
            directionalLight.type = LightType.Directional;
            
            Debug.Log("Created new Directional Light for cel shading");
        }
        
        // Configure the directional light
        directionalLight.color = lightColor;
        directionalLight.intensity = lightIntensity;
        directionalLight.transform.rotation = Quaternion.Euler(lightRotation);
        
        // Set as main light
        RenderSettings.sun = directionalLight;
        
        // Configure ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Flat;
        RenderSettings.ambientLight = ambientColor;
        RenderSettings.ambientIntensity = ambientIntensity;
        
        Debug.Log("Scene lighting setup complete for cel shading!");
        Debug.Log($"Directional Light: {directionalLight.name} - Intensity: {directionalLight.intensity}");
        Debug.Log($"Ambient Light: {RenderSettings.ambientLight} - Intensity: {RenderSettings.ambientIntensity}");
    }
    
    void OnValidate()
    {
        // Update lighting in real-time when values change in inspector
        if (Application.isPlaying)
        {
            SetupSceneLighting();
        }
    }
}
