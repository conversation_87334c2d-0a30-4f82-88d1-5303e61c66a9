{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20652, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20652, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20652, "tid": 9, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20652, "tid": 9, "ts": 1751296683896466, "dur": 1054, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20652, "tid": 9, "ts": 1751296683902120, "dur": 913, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20652, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20652, "tid": 1, "ts": 1751296683661341, "dur": 6637, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20652, "tid": 1, "ts": 1751296683667983, "dur": 75316, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20652, "tid": 1, "ts": 1751296683743314, "dur": 59027, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20652, "tid": 9, "ts": 1751296683903038, "dur": 1222, "ph": "X", "name": "", "args": {}}, {"pid": 20652, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683659187, "dur": 5796, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683664985, "dur": 218409, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683666261, "dur": 3142, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683669414, "dur": 1916, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683671337, "dur": 1383, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683672728, "dur": 16, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683672746, "dur": 215, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683672965, "dur": 9, "ph": "X", "name": "ProcessMessages 14336", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683672976, "dur": 49, "ph": "X", "name": "ReadAsync 14336", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673030, "dur": 3, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673034, "dur": 49, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673086, "dur": 1, "ph": "X", "name": "ProcessMessages 289", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673087, "dur": 72, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673164, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673231, "dur": 1, "ph": "X", "name": "ProcessMessages 881", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673234, "dur": 41, "ph": "X", "name": "ReadAsync 881", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673278, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673280, "dur": 29, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673446, "dur": 1, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673450, "dur": 50, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673503, "dur": 2, "ph": "X", "name": "ProcessMessages 1913", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673505, "dur": 42, "ph": "X", "name": "ReadAsync 1913", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673552, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673602, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673604, "dur": 61, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673669, "dur": 2, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673673, "dur": 69, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673750, "dur": 4, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673756, "dur": 73, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673832, "dur": 1, "ph": "X", "name": "ProcessMessages 721", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673835, "dur": 45, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673883, "dur": 1, "ph": "X", "name": "ProcessMessages 923", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673885, "dur": 44, "ph": "X", "name": "ReadAsync 923", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673932, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673934, "dur": 42, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673981, "dur": 3, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683673986, "dur": 54, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674043, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674045, "dur": 77, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674125, "dur": 1, "ph": "X", "name": "ProcessMessages 757", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674127, "dur": 51, "ph": "X", "name": "ReadAsync 757", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674181, "dur": 2, "ph": "X", "name": "ProcessMessages 750", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674184, "dur": 55, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674245, "dur": 3, "ph": "X", "name": "ProcessMessages 66", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674250, "dur": 77, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674330, "dur": 2, "ph": "X", "name": "ProcessMessages 1444", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674333, "dur": 53, "ph": "X", "name": "ReadAsync 1444", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674389, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674391, "dur": 50, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683674444, "dur": 5386, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683679839, "dur": 259, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680102, "dur": 14, "ph": "X", "name": "ProcessMessages 20517", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680117, "dur": 54, "ph": "X", "name": "ReadAsync 20517", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680175, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680179, "dur": 46, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680227, "dur": 1, "ph": "X", "name": "ProcessMessages 971", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680229, "dur": 46, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680278, "dur": 33, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680314, "dur": 57, "ph": "X", "name": "ReadAsync 1138", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680373, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680375, "dur": 60, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680439, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680441, "dur": 82, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680527, "dur": 1, "ph": "X", "name": "ProcessMessages 1037", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680530, "dur": 62, "ph": "X", "name": "ReadAsync 1037", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680607, "dur": 3, "ph": "X", "name": "ProcessMessages 1196", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680611, "dur": 68, "ph": "X", "name": "ReadAsync 1196", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680681, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680683, "dur": 136, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680821, "dur": 1, "ph": "X", "name": "ProcessMessages 661", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680855, "dur": 99, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680957, "dur": 2, "ph": "X", "name": "ProcessMessages 1839", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683680960, "dur": 102, "ph": "X", "name": "ReadAsync 1839", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681064, "dur": 2, "ph": "X", "name": "ProcessMessages 1954", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681067, "dur": 45, "ph": "X", "name": "ReadAsync 1954", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681114, "dur": 1, "ph": "X", "name": "ProcessMessages 925", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681116, "dur": 93, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681238, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681295, "dur": 149, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681446, "dur": 3, "ph": "X", "name": "ProcessMessages 4106", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681499, "dur": 88, "ph": "X", "name": "ReadAsync 4106", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681590, "dur": 1, "ph": "X", "name": "ProcessMessages 1338", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681593, "dur": 82, "ph": "X", "name": "ReadAsync 1338", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681677, "dur": 2, "ph": "X", "name": "ProcessMessages 1203", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683681680, "dur": 1018, "ph": "X", "name": "ReadAsync 1203", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683682703, "dur": 2, "ph": "X", "name": "ProcessMessages 614", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683682707, "dur": 220, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683682929, "dur": 11, "ph": "X", "name": "ProcessMessages 18166", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683682942, "dur": 30, "ph": "X", "name": "ReadAsync 18166", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683682975, "dur": 57, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683038, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683041, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683140, "dur": 4, "ph": "X", "name": "ProcessMessages 1576", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683145, "dur": 56, "ph": "X", "name": "ReadAsync 1576", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683206, "dur": 2, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683209, "dur": 63, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683275, "dur": 2, "ph": "X", "name": "ProcessMessages 997", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683278, "dur": 24, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683357, "dur": 73, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683434, "dur": 4, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683439, "dur": 63, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683504, "dur": 1, "ph": "X", "name": "ProcessMessages 2183", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683506, "dur": 40, "ph": "X", "name": "ReadAsync 2183", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683548, "dur": 36, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683587, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683590, "dur": 52, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683646, "dur": 44, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683693, "dur": 1, "ph": "X", "name": "ProcessMessages 782", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683695, "dur": 41, "ph": "X", "name": "ReadAsync 782", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683739, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683740, "dur": 43, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683785, "dur": 1, "ph": "X", "name": "ProcessMessages 864", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683787, "dur": 28, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683819, "dur": 1, "ph": "X", "name": "ProcessMessages 91", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683821, "dur": 40, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683862, "dur": 1, "ph": "X", "name": "ProcessMessages 712", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683864, "dur": 41, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683908, "dur": 1, "ph": "X", "name": "ProcessMessages 641", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683911, "dur": 52, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683979, "dur": 1, "ph": "X", "name": "ProcessMessages 629", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683683981, "dur": 34, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684018, "dur": 38, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684058, "dur": 1, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684060, "dur": 30, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684091, "dur": 1, "ph": "X", "name": "ProcessMessages 182", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684094, "dur": 58, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684155, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684193, "dur": 56, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684252, "dur": 25, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684279, "dur": 36, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684318, "dur": 37, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684357, "dur": 1, "ph": "X", "name": "ProcessMessages 656", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684358, "dur": 35, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684395, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684397, "dur": 60, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684462, "dur": 3, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684467, "dur": 49, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684517, "dur": 1, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684519, "dur": 29, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684550, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684553, "dur": 39, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684595, "dur": 28, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684625, "dur": 52, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684679, "dur": 38, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684719, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684721, "dur": 45, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684769, "dur": 30, "ph": "X", "name": "ReadAsync 919", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684802, "dur": 34, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684837, "dur": 33, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684873, "dur": 31, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684906, "dur": 33, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684941, "dur": 27, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684971, "dur": 26, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683684999, "dur": 30, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685031, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685033, "dur": 31, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685065, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685066, "dur": 56, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685128, "dur": 2, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685132, "dur": 34, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685169, "dur": 26, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685199, "dur": 29, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685231, "dur": 64, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685297, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685299, "dur": 36, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685337, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685339, "dur": 36, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685377, "dur": 3, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685381, "dur": 49, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685432, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685433, "dur": 41, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685477, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685479, "dur": 30, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685511, "dur": 33, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685549, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685600, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685602, "dur": 42, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685647, "dur": 93, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685743, "dur": 1, "ph": "X", "name": "ProcessMessages 268", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685745, "dur": 35, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685782, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685784, "dur": 52, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685839, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685841, "dur": 38, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685881, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685883, "dur": 46, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685933, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683685964, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686002, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686004, "dur": 32, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686039, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686040, "dur": 88, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686131, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686160, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686162, "dur": 32, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686196, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686198, "dur": 68, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686269, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686300, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686302, "dur": 26, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686331, "dur": 27, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686362, "dur": 65, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686431, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686470, "dur": 33, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686504, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686506, "dur": 25, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686534, "dur": 74, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686610, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686614, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686651, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686652, "dur": 25, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686680, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686681, "dur": 83, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686767, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686805, "dur": 1, "ph": "X", "name": "ProcessMessages 546", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686806, "dur": 29, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686838, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686839, "dur": 72, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686914, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686950, "dur": 28, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683686982, "dur": 27, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687012, "dur": 64, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687078, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687123, "dur": 32, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687157, "dur": 1, "ph": "X", "name": "ProcessMessages 605", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687159, "dur": 26, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687189, "dur": 67, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687258, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687292, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687294, "dur": 31, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687329, "dur": 25, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687357, "dur": 56, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687416, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687451, "dur": 1, "ph": "X", "name": "ProcessMessages 517", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687453, "dur": 33, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687489, "dur": 25, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687516, "dur": 58, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687577, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687610, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687611, "dur": 30, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687645, "dur": 27, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687675, "dur": 58, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687736, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687775, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687776, "dur": 68, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687847, "dur": 1, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687848, "dur": 72, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687923, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687950, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687953, "dur": 32, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687986, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683687988, "dur": 64, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688055, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688087, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688089, "dur": 27, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688117, "dur": 1, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688119, "dur": 70, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688191, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688223, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688224, "dur": 32, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688259, "dur": 28, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688290, "dur": 63, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688356, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688384, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688386, "dur": 32, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688420, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688422, "dur": 73, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688498, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688533, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688534, "dur": 31, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688568, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688570, "dur": 67, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688640, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688673, "dur": 31, "ph": "X", "name": "ReadAsync 200", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688706, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688708, "dur": 24, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688736, "dur": 70, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688810, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688844, "dur": 1, "ph": "X", "name": "ProcessMessages 690", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688846, "dur": 34, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688881, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688883, "dur": 36, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688920, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688922, "dur": 69, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683688996, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689061, "dur": 1, "ph": "X", "name": "ProcessMessages 1150", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689063, "dur": 40, "ph": "X", "name": "ReadAsync 1150", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689106, "dur": 29, "ph": "X", "name": "ReadAsync 697", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689138, "dur": 32, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689173, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689176, "dur": 30, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689208, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689210, "dur": 65, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689278, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689313, "dur": 31, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689347, "dur": 32, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689381, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689383, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689460, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689496, "dur": 40, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689539, "dur": 1, "ph": "X", "name": "ProcessMessages 752", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689541, "dur": 44, "ph": "X", "name": "ReadAsync 752", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689587, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689588, "dur": 66, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689657, "dur": 1, "ph": "X", "name": "ProcessMessages 274", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689659, "dur": 28, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689689, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689690, "dur": 30, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689723, "dur": 77, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689802, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689836, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689866, "dur": 47, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689917, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683689919, "dur": 224, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690152, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690232, "dur": 3, "ph": "X", "name": "ProcessMessages 2440", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690236, "dur": 52, "ph": "X", "name": "ReadAsync 2440", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690292, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690296, "dur": 46, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690345, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690347, "dur": 126, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690478, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690533, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690535, "dur": 44, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690584, "dur": 110, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690697, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690734, "dur": 37, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690773, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690775, "dur": 44, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690823, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690825, "dur": 90, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690918, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683690997, "dur": 3, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691002, "dur": 68, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691072, "dur": 2, "ph": "X", "name": "ProcessMessages 1950", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691078, "dur": 49, "ph": "X", "name": "ReadAsync 1950", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691131, "dur": 1, "ph": "X", "name": "ProcessMessages 293", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691133, "dur": 33, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691170, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691194, "dur": 166, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691367, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691446, "dur": 1, "ph": "X", "name": "ProcessMessages 769", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691448, "dur": 51, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691503, "dur": 2, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691506, "dur": 43, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691551, "dur": 1, "ph": "X", "name": "ProcessMessages 233", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691553, "dur": 46, "ph": "X", "name": "ReadAsync 233", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691605, "dur": 4, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691610, "dur": 55, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691669, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691705, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691707, "dur": 29, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691738, "dur": 1, "ph": "X", "name": "ProcessMessages 366", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691741, "dur": 72, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691815, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691870, "dur": 1, "ph": "X", "name": "ProcessMessages 729", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691872, "dur": 32, "ph": "X", "name": "ReadAsync 729", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691907, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691908, "dur": 25, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691935, "dur": 23, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683691961, "dur": 67, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692032, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692066, "dur": 1, "ph": "X", "name": "ProcessMessages 759", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692068, "dur": 32, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692102, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692104, "dur": 70, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692176, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692224, "dur": 42, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692267, "dur": 1, "ph": "X", "name": "ProcessMessages 861", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692269, "dur": 25, "ph": "X", "name": "ReadAsync 861", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692298, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692299, "dur": 61, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692364, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692417, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692418, "dur": 28, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692447, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692449, "dur": 74, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692526, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692564, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692566, "dur": 26, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692594, "dur": 67, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692664, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692701, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692702, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692734, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692735, "dur": 64, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692802, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692829, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692831, "dur": 36, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692869, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692871, "dur": 57, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692932, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692967, "dur": 1, "ph": "X", "name": "ProcessMessages 568", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683692969, "dur": 74, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693045, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693046, "dur": 34, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693083, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693084, "dur": 66, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693154, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693183, "dur": 73, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693260, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693291, "dur": 1, "ph": "X", "name": "ProcessMessages 657", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693293, "dur": 72, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693367, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693410, "dur": 34, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693446, "dur": 1, "ph": "X", "name": "ProcessMessages 562", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693449, "dur": 34, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693486, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693488, "dur": 79, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693572, "dur": 201, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693776, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693779, "dur": 49, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693831, "dur": 2, "ph": "X", "name": "ProcessMessages 1758", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693834, "dur": 54, "ph": "X", "name": "ReadAsync 1758", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693894, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693927, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693929, "dur": 47, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693979, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683693982, "dur": 31, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694017, "dur": 67, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694089, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694128, "dur": 1, "ph": "X", "name": "ProcessMessages 952", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694130, "dur": 34, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694166, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694170, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694235, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694270, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694272, "dur": 52, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694326, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694328, "dur": 80, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694413, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694454, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694457, "dur": 33, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694494, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694497, "dur": 52, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683694553, "dur": 1185, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683695742, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683695747, "dur": 187, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683695939, "dur": 12, "ph": "X", "name": "ProcessMessages 12432", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683695953, "dur": 38, "ph": "X", "name": "ReadAsync 12432", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683695993, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683695995, "dur": 31, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696030, "dur": 1, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696034, "dur": 28, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696066, "dur": 62, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696133, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696160, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696163, "dur": 174, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696341, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696344, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696393, "dur": 394, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696791, "dur": 92, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696890, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696893, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696961, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683696964, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697009, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697011, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697058, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697062, "dur": 41, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697106, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697110, "dur": 33, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697149, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697152, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697206, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697212, "dur": 35, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697251, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697254, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697301, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697304, "dur": 65, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697372, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697379, "dur": 50, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697432, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697435, "dur": 48, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697486, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697490, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697521, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697532, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697567, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683697575, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683700915, "dur": 4, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683700921, "dur": 358, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683701376, "dur": 180, "ph": "X", "name": "ProcessMessages 6016", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683701567, "dur": 11553, "ph": "X", "name": "ReadAsync 6016", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713129, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713134, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713175, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713177, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713254, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713257, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713292, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713296, "dur": 313, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713617, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713621, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713654, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713657, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713722, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713725, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713761, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713764, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713810, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683713814, "dur": 254, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683714073, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683714075, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683714115, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683714117, "dur": 4161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683718290, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683718295, "dur": 1393, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683719698, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683719704, "dur": 50, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683719757, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683719761, "dur": 191, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683719958, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683719961, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720013, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720014, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720066, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720070, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720110, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720113, "dur": 264, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720382, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720420, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720456, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720460, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720574, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720628, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720631, "dur": 40, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720674, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720678, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720720, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720722, "dur": 37, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720763, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720765, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720808, "dur": 29, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720840, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720842, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720886, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720889, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683720969, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721016, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721018, "dur": 33, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721054, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721058, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721174, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721176, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721208, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721213, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721246, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721249, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721284, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721330, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721362, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721364, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721420, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721422, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721463, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721465, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721500, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721533, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721536, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721571, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721573, "dur": 203, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721781, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683721784, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723533, "dur": 7, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723544, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723673, "dur": 6, "ph": "X", "name": "ProcessMessages 704", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723680, "dur": 23, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723707, "dur": 44, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723755, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723757, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723798, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723872, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723876, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723924, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683723927, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724014, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724017, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724057, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724087, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724090, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724119, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724123, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724163, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724194, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724247, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724249, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724283, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724286, "dur": 439, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724736, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724741, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724811, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724814, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724869, "dur": 4, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724874, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724926, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683724929, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725013, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725064, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725073, "dur": 51, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725128, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725132, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725187, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725191, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725240, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725242, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725283, "dur": 59, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725347, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725390, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725394, "dur": 215, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725615, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725667, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683725669, "dur": 1987, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683727665, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683727669, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683727763, "dur": 831, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683728608, "dur": 110, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683728722, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683728725, "dur": 429, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729159, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729161, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729218, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729220, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729262, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729263, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729292, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729458, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729503, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729539, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729575, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729765, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729769, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729822, "dur": 169, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683729994, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730035, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730037, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730077, "dur": 387, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730468, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730521, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730523, "dur": 178, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730707, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730752, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683730754, "dur": 1458, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683732221, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683732225, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683732274, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683732277, "dur": 114693, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683846979, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683846983, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683847011, "dur": 1373, "ph": "X", "name": "ProcessMessages 205", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683848388, "dur": 10975, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859373, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859378, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859413, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859415, "dur": 70, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859488, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859518, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859520, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859605, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683859636, "dur": 655, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683860294, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683860315, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683860443, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683860468, "dur": 1460, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683861932, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683861934, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683861962, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683861964, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683861995, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683861997, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862093, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862097, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862132, "dur": 204, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862341, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862379, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862426, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862428, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862460, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683862462, "dur": 874, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683863342, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683863374, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683863376, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683863432, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683863459, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683863462, "dur": 1069, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864534, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864566, "dur": 98, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864668, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864670, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864709, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864712, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864751, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864804, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864837, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864838, "dur": 110, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864952, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864982, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683864984, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683865017, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683865045, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683865162, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683865165, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683865197, "dur": 1300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866504, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866507, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866535, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866537, "dur": 126, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866667, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866671, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866712, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683866714, "dur": 701, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867419, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867451, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867453, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867511, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867513, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867552, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867554, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867582, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867675, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867709, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867712, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867747, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867749, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867780, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867807, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867833, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867835, "dur": 23, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867861, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867896, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683867925, "dur": 115, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868043, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868074, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868100, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868133, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868161, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868191, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868193, "dur": 26, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868220, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868223, "dur": 23, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868249, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868274, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868337, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868374, "dur": 5, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868381, "dur": 31, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868414, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868416, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868446, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868482, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868484, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868518, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868559, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868589, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868591, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868622, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868653, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868678, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868719, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868721, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868766, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868799, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868858, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868884, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868886, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868952, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868954, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868996, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683868998, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869035, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869037, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869063, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869101, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869128, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869159, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869184, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869252, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869292, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869294, "dur": 132, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869429, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869457, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869459, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869491, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869541, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869543, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869583, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869617, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869619, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869658, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869701, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869703, "dur": 40, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869746, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869748, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869792, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869795, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869838, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869840, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869872, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869926, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869970, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683869972, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870022, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870024, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870055, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870058, "dur": 245, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870308, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870343, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870344, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870377, "dur": 178, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870560, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870598, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870600, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870644, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870646, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870691, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870694, "dur": 46, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870743, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870745, "dur": 49, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870799, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870834, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870836, "dur": 76, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870915, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683870917, "dur": 540, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683871462, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683871464, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683871513, "dur": 288, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 20652, "tid": 12884901888, "ts": 1751296683871804, "dur": 10636, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20652, "tid": 9, "ts": 1751296683904262, "dur": 1276, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20652, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20652, "tid": 8589934592, "ts": 1751296683655644, "dur": 146746, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20652, "tid": 8589934592, "ts": 1751296683802393, "dur": 5, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20652, "tid": 8589934592, "ts": 1751296683802399, "dur": 1244, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20652, "tid": 9, "ts": 1751296683905541, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20652, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20652, "tid": 4294967296, "ts": 1751296683620502, "dur": 263894, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751296683627893, "dur": 19622, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751296683884639, "dur": 8347, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751296683888368, "dur": 3109, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20652, "tid": 4294967296, "ts": 1751296683893086, "dur": 19, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20652, "tid": 9, "ts": 1751296683905553, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751296683662238, "dur": 54, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683662316, "dur": 2551, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683664890, "dur": 1074, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683666146, "dur": 90, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751296683666237, "dur": 562, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683668087, "dur": 1207, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683670285, "dur": 2153, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683673354, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683673562, "dur": 192, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751296683675055, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751296683676266, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Runtime.ref.dll_35E0707C8501A09D.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683676598, "dur": 4280, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751296683681119, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751296683682170, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.Editor.ref.dll_E03858E727F23F6F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683683349, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751296683683517, "dur": 198, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751296683684238, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683684958, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683685832, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751296683686428, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751296683690351, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751296683690869, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751296683691817, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751296683693731, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1751296683694330, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751296683696449, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1751296683696553, "dur": 167, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751296683666838, "dur": 30072, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683696924, "dur": 174714, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683871639, "dur": 294, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683872094, "dur": 83, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683872216, "dur": 1479, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751296683667274, "dur": 29728, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683697015, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_51B6A25823037467.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683697822, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683697882, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_BADDD17E000661FB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683698028, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_433711FCB6016F00.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683698124, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_B6153E13308654D1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683698207, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_0B2798972D9D68EB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683698262, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683698354, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_80843626DE95EA13.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683698454, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_486DF76931BD0557.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683698635, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683698730, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_14A5559FFED55C50.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683698866, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683698963, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridAndSnapModule.dll_1FF1C2755BAA46D4.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683699187, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751296683699479, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683699580, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751296683699769, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751296683700394, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751296683700584, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751296683700742, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751296683701099, "dur": 1444, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683702544, "dur": 2644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683705190, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683705456, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683705770, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683706004, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683706246, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683706498, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683706764, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683707169, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683707470, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683707691, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683707953, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683708361, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683708656, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.State\\Transitions\\NesterStateTransitionWidget.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751296683708588, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683709526, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683709846, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683710144, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683710368, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683710631, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683710873, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683711528, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683711776, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683712016, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683712252, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683712475, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683712758, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683713062, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683713354, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683713584, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683713941, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683714189, "dur": 478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683714668, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683714982, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683715254, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683715521, "dur": 300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683715822, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683716148, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683716429, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683716662, "dur": 93, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683716815, "dur": 301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683717117, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683717604, "dur": 1446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683719093, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683720114, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683720279, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683720364, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683721215, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683721460, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683721726, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683721785, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683722195, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683722844, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683723020, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683723210, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683723456, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683723669, "dur": 993, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683724674, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683724783, "dur": 864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683725672, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683725769, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.CodeGen.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751296683725824, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683726180, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683726353, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683726413, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683726947, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683727065, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683727145, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683727369, "dur": 890, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683728262, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683728405, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751296683728680, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683729331, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683729433, "dur": 1877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683731311, "dur": 125979, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683857291, "dur": 2796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683860088, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683860271, "dur": 2807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683863156, "dur": 2507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683865729, "dur": 2469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683868199, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751296683868283, "dur": 3102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751296683871482, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683667197, "dur": 29783, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683696988, "dur": 622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_33AFB99E7761AA5C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683697653, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_308026626194FDB8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683697721, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683697809, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_BFD5601BA05C7CD8.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683697915, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_E04F87D224606974.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683698053, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_BB587A5119495C88.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683698168, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_DA7D4247AC2DB3F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683698272, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_CBABBF352E91654E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683698346, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683698442, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_1FC72A0013DC4EEB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683698665, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683698723, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_B566D0CB2E423B86.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683698840, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.ShaderFoundryModule.dll_8325F75A1495950D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683698969, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_C01CA8838C1CE31C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683699101, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_BFEF6BF3A445E475.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683699513, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683699610, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751296683699855, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751296683699963, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683700089, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751296683700220, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683700599, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683700685, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751296683700786, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751296683701065, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6947401630772442630.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751296683701149, "dur": 2513, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683703663, "dur": 1973, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683705637, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683705893, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683706123, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683706373, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683706807, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683707226, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683707693, "dur": 508, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683708202, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683708549, "dur": 774, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Runtime\\2D\\Rendergraph\\DrawNormal2DPass.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751296683709366, "dur": 715, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Runtime\\2D\\Rendergraph\\CopyCameraSortingLayerPass.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751296683708436, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683710207, "dur": 370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683710588, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683711225, "dur": 2593, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector3\\Vector3DotProduct.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751296683711212, "dur": 2915, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683714127, "dur": 869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683714996, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683715302, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683715548, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683715870, "dur": 671, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Actions\\InputActionParameters.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751296683715820, "dur": 995, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683716815, "dur": 325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683717141, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683718389, "dur": 671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683719060, "dur": 997, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683720059, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683720238, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683720327, "dur": 1684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683722012, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683722200, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683722335, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683722554, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683722672, "dur": 995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683723669, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683723969, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683724428, "dur": 1618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683726051, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683726190, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683726388, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683726967, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683727061, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683727139, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683727316, "dur": 726, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683728131, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751296683728327, "dur": 620, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683728948, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683729037, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683729144, "dur": 2161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683731306, "dur": 125986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683857294, "dur": 2740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683860036, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683860289, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683863010, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683863206, "dur": 2663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683865870, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683865941, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751296683868490, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683868703, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683868845, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683868997, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683869153, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683869489, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683869641, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683869923, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683870287, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751296683870533, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683870656, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683870746, "dur": 591, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751296683871373, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683667112, "dur": 29827, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683696986, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683697124, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_4E1AB9DAA17812DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683697758, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_BA0CB78B239CCACF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683697865, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_8D9A2E8777AB6882.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683697961, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_F0EAC96F0603CD08.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683698057, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_8FE2697BF98A2193.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683698291, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_5FDF6992734E0361.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683698408, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AFE94C31B12D3213.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683698543, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_76EB92ADF25C401C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683698782, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_596B249A76101BE6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683698883, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_A1485D01311C863E.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683699095, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683699214, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AMDModule.dll_7F3203F2669A3DC6.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683699329, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683699435, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751296683699545, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683699733, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683699896, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751296683699952, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683700126, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683700366, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751296683700483, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683700547, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683700778, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751296683700899, "dur": 152, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751296683701084, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683701144, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683705765, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751296683703315, "dur": 3184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683706499, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683706781, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683707229, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683707504, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683708651, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Graphs\\SerializableTextureArray.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751296683708240, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683709369, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683709634, "dur": 374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683710009, "dur": 718, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683710728, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683711215, "dur": 2291, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4DotProduct.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751296683711171, "dur": 2527, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683713698, "dur": 504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683714203, "dur": 402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683714606, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683714876, "dur": 362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683715238, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683715479, "dur": 252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683715731, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683716025, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683716267, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683716492, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683716780, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683717100, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683717608, "dur": 1447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683719056, "dur": 1004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683720066, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683720243, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683720374, "dur": 1772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683722147, "dur": 385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683722616, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683722906, "dur": 2260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683725167, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683725479, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683725642, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683726016, "dur": 1437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683727453, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683727569, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751296683727738, "dur": 546, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683728287, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683728376, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683728472, "dur": 2843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683731316, "dur": 126007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683857335, "dur": 2723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683860059, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683860127, "dur": 2512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683862640, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683862762, "dur": 2716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683865479, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683865577, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683868135, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683868197, "dur": 3081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751296683871353, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751296683871469, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683667179, "dur": 29788, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683696986, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683697126, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_C8A867A03915DFB5.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683697665, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GraphicsStateCollectionSerializerModule.dll_A164BAE5670FC544.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683697786, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputForUIModule.dll_60AF5DD29DC33809.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683697926, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_BEB6379C07D3D185.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698024, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_FE92BD4D4925733A.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698101, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_77F91A8BF54EFB7C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698192, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_7E47E9336205E31D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698366, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683698427, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_20A6B658F2F96E47.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698515, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AccessibilityModule.dll_EAAAA0867963BCF7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698679, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683698825, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_467B09015CCE177C.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698925, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PhysicsModule.dll_CC3B7EF1A2C4EA40.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683698982, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683699065, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_0D77228FB13B7FB6.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683699601, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683699751, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683699863, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751296683699963, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683700148, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683700490, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683700675, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751296683700755, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751296683700911, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751296683701109, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683701183, "dur": 2143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683703328, "dur": 2256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683705584, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683705876, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683706107, "dur": 462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683706570, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683706836, "dur": 532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683707369, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683707613, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683708061, "dur": 1301, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Nodes\\Input\\Geometry\\ScreenPositionNode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751296683707847, "dur": 1551, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683709398, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683709659, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683709940, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683710239, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683710492, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683710790, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683711210, "dur": 1455, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\SetObjectVariable.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751296683711076, "dur": 1674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683712751, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683713017, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683713260, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683713502, "dur": 640, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683714143, "dur": 357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683714501, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683715390, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections@56bff8827a7e\\Unity.Collections\\UnsafeBitArray.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751296683714900, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683715999, "dur": 739, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@8017b507cc74\\Unity.Mathematics\\bool2x2.gen.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751296683715934, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683716905, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683717609, "dur": 1444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683719054, "dur": 1012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683720069, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683720243, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683720345, "dur": 361, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683720708, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683721567, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683721928, "dur": 1920, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683723974, "dur": 387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683724419, "dur": 1356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683725786, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683725896, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683725967, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683726202, "dur": 2927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683729132, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751296683729350, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683729916, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683729987, "dur": 1317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683731304, "dur": 125992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683857322, "dur": 2947, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683860270, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683860377, "dur": 2373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683862751, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751296683862861, "dur": 2693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683865607, "dur": 2534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683868201, "dur": 3177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751296683871450, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683667257, "dur": 29734, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683697000, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_8EA224AC6679B7C2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683697861, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_DBB153F07C192DFF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683697994, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_472976495F1230C7.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683698095, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_1A2A3B79BD3B4CFD.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683698363, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683698428, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683698488, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_3EF742C8CA4C5E1E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683698542, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_9843DD54C5E6159E.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683698718, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_15A0A72BE0744047.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683698835, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SketchUpModule.dll_F697CEAF687CB7A2.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683698956, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.MultiplayerModule.dll_5F13DCF03A10B823.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683699014, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683699099, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683699410, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_3D651E01942E03F8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683699517, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683699665, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683699880, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751296683700073, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751296683700156, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683700466, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683700562, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751296683700720, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751296683700849, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751296683700930, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751296683701096, "dur": 456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683701553, "dur": 1896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683704207, "dur": 1028, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Json.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751296683705433, "dur": 500, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Core.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751296683706413, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpOverrides.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751296683703450, "dur": 3913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683707364, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683707618, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683707936, "dur": 680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683708660, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.State\\States\\StateAnalysis.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751296683708617, "dur": 955, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683709786, "dur": 1455, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Runtime\\Memory\\PinnedArray.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751296683709573, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683711435, "dur": 1619, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Scalar\\ScalarRound.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751296683711270, "dur": 1891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683713162, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683713437, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683713763, "dur": 710, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerBitField.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751296683713680, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683714663, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683714917, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683715212, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683715448, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683715872, "dur": 815, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.inputsystem@7fe8299111a7\\InputSystem\\Controls\\Processors\\NormalizeVector2Processor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751296683715702, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683716933, "dur": 539, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@f6ed7fd5ec8f\\UnityEditor.TestRunner\\TestRun\\Tasks\\Scene\\SaveModifiedSceneTask.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751296683716824, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683717720, "dur": 1327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683719094, "dur": 995, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683720091, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683720264, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683720369, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751296683721069, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683721158, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683721222, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683721488, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751296683721953, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751296683723066, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683723185, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683723486, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751296683724100, "dur": 401, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683724509, "dur": 1142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683725652, "dur": 557, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683726210, "dur": 5088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683731300, "dur": 77240, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683808541, "dur": 48768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683857314, "dur": 2737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751296683860053, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683860116, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751296683862630, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683862713, "dur": 2703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751296683865417, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751296683865502, "dur": 2756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751296683868309, "dur": 3225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683667320, "dur": 29703, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683697035, "dur": 716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683697753, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683697824, "dur": 114, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_E5318FF293833B30.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683697965, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ShaderVariantAnalyticsModule.dll_209987431F1A3EDC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683698075, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683698146, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_D4344483B74B03A7.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683698269, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_584BDBB0AACB340C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683698369, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_1948650C4D21EE20.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683698523, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextRenderingModule.dll_8DE949524442C9D8.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683698716, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683698806, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SubstanceModule.dll_69FC355D1BD4BB29.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683698894, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683699006, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_9870EDF59A107BDF.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683699077, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683699166, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683699348, "dur": 14966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683714466, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683714551, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683714836, "dur": 4077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683718915, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683719063, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683719155, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683719330, "dur": 558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683719888, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683720084, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683720272, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683721060, "dur": 551, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683721785, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683722011, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683722550, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683722730, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683723060, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683723687, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683723803, "dur": 333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683724137, "dur": 1517, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683725654, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683726188, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751296683726378, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683727157, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683727213, "dur": 4078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683731292, "dur": 73112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683806704, "dur": 332, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 6, "ts": 1751296683807037, "dur": 1179, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 6, "ts": 1751296683808218, "dur": 273, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 6, "ts": 1751296683804405, "dur": 4093, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683808499, "dur": 48785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683857286, "dur": 3729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683861080, "dur": 2592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683863673, "dur": 531, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683864215, "dur": 2674, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683866890, "dur": 539, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683867446, "dur": 3577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751296683871092, "dur": 335, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751296683871427, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683667371, "dur": 29671, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683697052, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_595F986B2A245834.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683697710, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_EE062F8861099912.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683697829, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683697922, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_DB48149D0776AF57.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698049, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_D8FCB30ADD921A73.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698134, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698247, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_93B77B82222D1B4A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698304, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698495, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_A28CE54978CD971D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698553, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.XRModule.dll_DAD9D37CE1A4BA55.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698743, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TreeModule.dll_0333895C18D609C9.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698843, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_FECC8B8B813FB02A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683698904, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683699068, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_7AC1C063560F2BC6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683699547, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751296683699640, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751296683699789, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751296683699946, "dur": 100, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751296683700293, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683700579, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13686715218353603589.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751296683700669, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751296683700868, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7759033573704817190.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751296683701135, "dur": 2662, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683705275, "dur": 1000, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-processthreads-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1751296683703797, "dur": 3221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683707019, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683707326, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683707573, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683707817, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683708123, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683708658, "dur": 573, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Runtime\\2D\\Shadows\\ShadowProvider\\EdgeDictionary.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751296683708372, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683709331, "dur": 308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683709640, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683710037, "dur": 1192, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Editor\\Lighting\\Shadow\\ShadowCascadeGUI.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751296683709898, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683711359, "dur": 265, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683711624, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683711871, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683712145, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683712377, "dur": 491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683712869, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683713122, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683713365, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683713616, "dur": 446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683714063, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683714513, "dur": 315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683714828, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683715174, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683715477, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683715818, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683716123, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683716408, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683716635, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683716919, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683717199, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683718345, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683719095, "dur": 1001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683720100, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683720264, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683720347, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683721431, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683721614, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683721820, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683722041, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683722108, "dur": 898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683723015, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683723087, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683723315, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683723526, "dur": 1039, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683724568, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683724853, "dur": 791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683725647, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683725975, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683726031, "dur": 1650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683727683, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683727787, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683727972, "dur": 962, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683728936, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683729031, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683729117, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683729329, "dur": 645, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683730060, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751296683730229, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683730811, "dur": 518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683731330, "dur": 125957, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683857289, "dur": 2990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683860281, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683860396, "dur": 2667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683863139, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683865720, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683865800, "dur": 2683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751296683868617, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683869045, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683869108, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683869274, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683869396, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683869559, "dur": 306, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683869953, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683870041, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683870272, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683870545, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683870732, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683870803, "dur": 597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751296683871429, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683667418, "dur": 29643, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683697079, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_C047F56604413718.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683697650, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_5DFD03B93DA29B4D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683697779, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_91BF37D1F055EC15.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683697973, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_3E1DF6609ACAC61F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698036, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683698106, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_7DC1C7578DCAB919.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698212, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_C1BEC5DDD68DA9AF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698314, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_815CA55F1B3FC2AD.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698404, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_F21194D2F617C9ED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698512, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_922751811D286E63.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698734, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_D32EF602AAA54BE6.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698847, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_44462716F1E6187F.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683698959, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GridModule.dll_82F8B03BA9650A81.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683699011, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683699139, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683699218, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751296683699356, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751296683699568, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751296683699834, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683699930, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751296683700182, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683700245, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751296683700315, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683700450, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683700565, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751296683700931, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751296683701133, "dur": 2568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683703702, "dur": 1786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683705489, "dur": 306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683705795, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683706029, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683706237, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683706484, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683706754, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683707043, "dur": 366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683707570, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Drawing\\Inspector\\PropertyDrawers\\SampleTexture2DNodePropertyDrawer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751296683707410, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683708287, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683708661, "dur": 656, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@89399b10acbb\\Runtime\\2D\\Light2DAuthoring.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751296683708522, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683709507, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683709763, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683710062, "dur": 1034, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Editor\\EditorPrefBool.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751296683710005, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683711425, "dur": 1210, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Multiply.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751296683711286, "dur": 1469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683712837, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Interface\\Fuzzy\\FuzzyGroupOption.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751296683712755, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683713760, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Runtime\\LookDev\\IDataProvider.cs"}}, {"pid": 12345, "tid": 8, "ts": 1751296683713533, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683714294, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683714627, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683714907, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683715192, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683715428, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683715672, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683715996, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683716275, "dur": 280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683716555, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683716836, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683717159, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683718359, "dur": 717, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683719077, "dur": 1007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683720100, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683720261, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683720363, "dur": 1113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683721485, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683721753, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683722052, "dur": 782, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683722835, "dur": 499, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683723399, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683723635, "dur": 1038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683724674, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683724836, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683725029, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683725636, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683725784, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683726185, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683726383, "dur": 499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683726882, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683727091, "dur": 4197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683731306, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751296683731495, "dur": 125843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683857341, "dur": 2630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683859973, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683860099, "dur": 2529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683862629, "dur": 535, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683863172, "dur": 2550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683865723, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683865796, "dur": 2635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751296683868607, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683868686, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683869017, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683869255, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751296683869343, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683869647, "dur": 590, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683870275, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683870739, "dur": 343, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683871117, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751296683871452, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683667461, "dur": 29640, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683697116, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_9D64899DE49D3B06.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683697705, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_A619838E516A75AC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683697776, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683697877, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683697961, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MultiplayerModule.dll_64D9AC7F4C4AC8A8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698034, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubstanceModule.dll_1FE2264601546D0E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698145, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683698230, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_48483D327EA17808.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698359, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_E689A207C3E7A0C2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698460, "dur": 167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_A587944D9110748A.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698667, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VFXModule.dll_F27EBD72F4881481.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698730, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683698789, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_1182EE28E68C62C8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698852, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683698929, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_256223B421F6FD72.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683698989, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683699069, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_E106F825F731F4BC.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683699134, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683699312, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683699514, "dur": 13283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683712799, "dur": 1019, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683713869, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683714039, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683714659, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683714929, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683715415, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683715659, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683715920, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683716162, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683716405, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683716643, "dur": 286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683716929, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683717220, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683718189, "dur": 918, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683719109, "dur": 1189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683720300, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683720519, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683720669, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683720855, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683721042, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683721332, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683721838, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683722500, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683722732, "dur": 679, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683723412, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683723665, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683724102, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683724872, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683725605, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683725943, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683726013, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683726195, "dur": 1946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683728143, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683728343, "dur": 1885, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683730230, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683730344, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683730517, "dur": 680, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683731295, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1751296683731468, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683731874, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683732268, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683732621, "dur": 124706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683857336, "dur": 3834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683861219, "dur": 2637, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683863858, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683864113, "dur": 2724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683866838, "dur": 395, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683867247, "dur": 2898, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1751296683870146, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683870282, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683870370, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683870605, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683870750, "dur": 646, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1751296683871455, "dur": 211, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683667506, "dur": 29625, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683697134, "dur": 553, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_67CB883191F4D66D.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683697737, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0FEAE2B241ACD344.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683697964, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_8B3842F7B08DFF89.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683698079, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_D7CA9FED890DDAB1.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683698446, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_1638DBF3D84589E5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683698511, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683698636, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.VideoModule.dll_B83C6071BB394334.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683698699, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683698772, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TilemapModule.dll_857E61E190401437.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683698866, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SafeModeModule.dll_0E82E95F1D4B1B8B.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683698978, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683699128, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683699453, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683699592, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751296683699650, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683700053, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751296683700448, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683700581, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3064155424177982801.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751296683700779, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751296683700907, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1751296683701106, "dur": 2465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683703949, "dur": 969, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Html.Abstractions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1751296683703572, "dur": 2460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683706033, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683706269, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683707099, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683707407, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683707647, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683708060, "dur": 1300, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Nodes\\Artistic\\Normal\\NormalFromTextureNode.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751296683707947, "dur": 1596, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683709543, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683709805, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683710118, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683710360, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683710609, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683710926, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683711170, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683711414, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683711698, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683711946, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683712178, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683712410, "dur": 520, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683712930, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683713165, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683713386, "dur": 593, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683713980, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683714355, "dur": 615, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Reflection\\IAttributeProvider.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751296683714215, "dur": 875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683715091, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683715344, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683715585, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683715841, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683716187, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683716418, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683716720, "dur": 747, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@8a519b6be09c\\Editor\\UGUI\\UI\\PropertyDrawers\\AnimationTriggersDrawer.cs"}}, {"pid": 12345, "tid": 10, "ts": 1751296683716652, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683717792, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683719121, "dur": 1164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683720287, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683720490, "dur": 1655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683722146, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683722283, "dur": 1140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683723424, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683723662, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683724135, "dur": 1526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683725661, "dur": 520, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683726183, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683726398, "dur": 469, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683726869, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683726993, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683727066, "dur": 4220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683731289, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1751296683731499, "dur": 125843, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683857344, "dur": 2630, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683859986, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683860091, "dur": 2548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683862640, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683862722, "dur": 2616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683865339, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1751296683865444, "dur": 2974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683868469, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1751296683871480, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683667540, "dur": 29661, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683697202, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_526B70D8724FDBD0.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683697851, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_D7031BE9F197A0A6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683697959, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683698037, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B1D6DCAB2D5A18DA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683698217, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_D1635D1E3A6F959D.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683698317, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_FEA05FC664382286.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683698391, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683698489, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_228E07120E318326.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683698690, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_1DC8148833156840.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683698802, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TerrainModule.dll_E681FA607883332E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683698903, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PropertiesModule.dll_8A89D5AFAE49E63E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683699131, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683699199, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_07FA64190ECA4CC1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683699273, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683699391, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751296683699455, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683699525, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683699595, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751296683699685, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751296683699979, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683700154, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683700318, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683700508, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683700573, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10397561839769426034.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751296683700781, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12812556936427222528.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1751296683701052, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683703112, "dur": 607, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751296683701124, "dur": 2701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683704505, "dur": 593, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751296683705439, "dur": 804, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-datetime-l1-1-0.dll"}}, {"pid": 12345, "tid": 11, "ts": 1751296683703825, "dur": 2688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683706514, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683707112, "dur": 1089, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Util\\MessageManager.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751296683706864, "dur": 1427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683708394, "dur": 986, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@26dc5ae27e7d\\Editor\\Data\\Graphs\\GraphConcretization.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751296683708291, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683709537, "dur": 356, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683710032, "dur": 1436, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@a7356ab905fd\\Editor\\Material\\MaterialEditorExtension.cs"}}, {"pid": 12345, "tid": 11, "ts": 1751296683709893, "dur": 1676, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683711570, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683711810, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683712101, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683712349, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683712637, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683712902, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683713134, "dur": 285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683713420, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683713675, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683714062, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683714359, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683714769, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683715208, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683715544, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683715822, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683716095, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683716395, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683716648, "dur": 368, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683717017, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683717296, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683717665, "dur": 1494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683719159, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683720093, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683720268, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683720358, "dur": 685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683721044, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683721391, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683721558, "dur": 856, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683722416, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683722609, "dur": 421, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683723084, "dur": 649, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683723738, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683723874, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683723972, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1751296683724390, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683724547, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683725517, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683725619, "dur": 964, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1751296683726585, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683726646, "dur": 193, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683727424, "dur": 120307, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 11, "ts": 1751296683857305, "dur": 2790, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683860152, "dur": 2477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683862630, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683862704, "dur": 2545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683865307, "dur": 2689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683867997, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1751296683868329, "dur": 3015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1751296683871468, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683667572, "dur": 29600, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683697173, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0999ABDFA115F312.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683697731, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683697807, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_7676AB112B74F121.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683697955, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_658DA73782B14E20.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683698057, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683698164, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_7B8E45163E4907D2.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683698328, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_7E691CD5ECB673D9.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683698387, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683698465, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_12124AFB436F9D5D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683698636, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683698694, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UmbraModule.dll_0E07881F85CDD302.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683698810, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteShapeModule.dll_F8CCEDBE36EB62B5.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683698917, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_7B82E1658E753B21.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683698978, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683699542, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683699600, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751296683699652, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683699708, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683699780, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751296683699859, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751296683700123, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683700211, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683700769, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1751296683701081, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683703893, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.1f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\mscorlib.dll"}}, {"pid": 12345, "tid": 12, "ts": 1751296683701547, "dur": 3092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683704640, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683704833, "dur": 332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683705166, "dur": 317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683705484, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683705789, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683706010, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683706243, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683706778, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683707180, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683707437, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683707684, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683708028, "dur": 554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683708669, "dur": 638, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Editor\\BurstLoader.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751296683709308, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@59eb6f11d242\\Editor\\BurstInspectorGUI.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751296683708583, "dur": 1417, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683710000, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683710273, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683710550, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683711099, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683711668, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683711885, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683712148, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683712580, "dur": 1898, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Editor\\VisualScripting.Core\\Plugin\\Changelogs\\LegacyLudiqCore\\Changelog_1_4_11.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751296683712382, "dur": 2160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683714588, "dur": 1326, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@7dcdc439b230\\Runtime\\VisualScripting.Core\\Graphs\\IGraphNest.cs"}}, {"pid": 12345, "tid": 12, "ts": 1751296683714587, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683716164, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683716424, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683716675, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683716734, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683716988, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683717660, "dur": 1472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683719133, "dur": 967, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683720103, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683720260, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683720380, "dur": 805, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683721187, "dur": 280, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683721520, "dur": 1213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683722744, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683722897, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683723140, "dur": 1233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683724374, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683724559, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683724640, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1751296683724880, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683724954, "dur": 681, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683725637, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683725984, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683726209, "dur": 5093, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683731303, "dur": 126015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683857322, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683860036, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683860140, "dur": 2562, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683862704, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683862766, "dur": 2792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683865559, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1751296683865737, "dur": 2670, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683868455, "dur": 2888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1751296683871408, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751296683880688, "dur": 1852, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20652, "tid": 9, "ts": 1751296683906162, "dur": 2585, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20652, "tid": 9, "ts": 1751296683908815, "dur": 2477, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20652, "tid": 9, "ts": 1751296683900306, "dur": 12436, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}